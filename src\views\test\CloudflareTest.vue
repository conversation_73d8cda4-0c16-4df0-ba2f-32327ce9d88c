<template>
  <div class="cloudflare-test">
    <div class="test-header">
      <h2>🔐 Cloudflare Turnstile 验证测试 (新版)</h2>
      <p>使用新的弹窗组件进行Cloudflare验证测试</p>
    </div>

    <!-- 功能选项卡 -->
    <van-tabs v-model:active="activeTab" @change="onTabChange">
      <!-- 弹窗验证测试 -->
      <van-tab title="弹窗验证" name="dialog">
        <div class="tab-content">
          <div class="section">
            <h3>🚀 弹窗验证测试</h3>
            <div class="test-controls">
              <van-button
                type="primary"
                @click="testDialogVerification"
                :loading="isTestingDialog"
                block
              >
                测试弹窗验证
              </van-button>

              <van-button
                type="success"
                @click="testLoginVerification"
                :loading="isTestingLogin"
                block
                style="margin-top: 12px"
              >
                测试登录验证
              </van-button>

              <van-button
                type="warning"
                @click="testKYCVerification"
                :loading="isTestingKYC"
                block
                style="margin-top: 12px"
              >
                测试KYC验证
              </van-button>

              <van-button
                type="primary"
                @click="testSeamlessVerification"
                :loading="isTestingSeamless"
                block
                style="margin-top: 12px"
              >
                测试executeSeamlessVerification
              </van-button>
            </div>
          </div>

          <div class="section">
            <h3>📱 快捷验证方法</h3>
            <div class="quick-test-grid">
              <van-button size="small" @click="quickTestLogin">登录验证</van-button>
              <van-button size="small" @click="quickTestRegister">注册验证</van-button>
              <van-button size="small" @click="quickTestKYC">KYC验证</van-button>
              <van-button size="small" @click="quickTestWithdrawal">提款验证</van-button>
              <van-button size="small" @click="quickTestForgetPW">忘记密码</van-button>
              <van-button size="small" @click="quickTestChangePW">修改密码</van-button>
            </div>
          </div>
        </div>
      </van-tab>

      <!-- 组件验证测试 -->
      <van-tab title="组件验证" name="component">
        <div class="tab-content">
          <div class="section">
            <h3>🧩 组件直接调用</h3>
            <div class="test-controls">
              <van-button type="primary" @click="showComponentDialog = true" block>
                显示验证组件
              </van-button>
            </div>
          </div>
        </div>
      </van-tab>

      <!-- 环境诊断 -->
      <van-tab title="环境诊断" name="diagnostic">
        <div class="tab-content">
          <div class="section">
            <h3>🌐 环境信息</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">当前环境:</span>
                <span class="value">{{ currentEnv }}</span>
              </div>
              <div class="info-item">
                <span class="label">操作系统:</span>
                <span class="value">{{ osType }}</span>
              </div>
              <div class="info-item">
                <span class="label">是否原生:</span>
                <span class="value">{{ isNative ? "是" : "否" }}</span>
              </div>
              <div class="info-item">
                <span class="label">Site Key:</span>
                <span class="value monospace">{{ currentSiteKey }}</span>
              </div>
            </div>
          </div>

          <div class="section">
            <h3>🔧 诊断操作</h3>
            <div class="diagnostic-buttons">
              <van-button @click="runDiagnostic" :loading="isDiagnosing"> 运行诊断 </van-button>
              <van-button @click="checkSiteKey"> 检查 Site Key </van-button>
              <van-button @click="testScriptLoading"> 测试脚本 </van-button>
              <van-button @click="clearCache" type="warning"> 清除缓存 </van-button>
            </div>
          </div>
        </div>
      </van-tab>

      <!-- 外链配置 -->
      <van-tab title="外链配置" name="external">
        <div class="tab-content">
          <div class="section">
            <h3>⚙️ 外链配置</h3>
            <div class="config-form">
              <van-field
                v-model="externalConfig.siteKey"
                label="Site Key"
                placeholder="选择 Site Key"
                readonly
                @click="showSiteKeyPicker = true"
              />
              <van-field
                v-model="externalConfig.appearance"
                label="外观设置"
                placeholder="选择外观"
                readonly
                @click="showAppearancePicker = true"
              />
              <van-field
                v-model="externalConfig.host"
                label="外链域名"
                placeholder="输入外链域名"
              />
            </div>

            <div class="external-actions">
              <van-button type="primary" @click="previewExternalUrl" block>
                预览外链 URL
              </van-button>
              <van-button type="default" @click="copyExternalUrl" block style="margin-top: 12px">
                复制外链 URL
              </van-button>
            </div>
          </div>
        </div>
      </van-tab>

      <!-- 测试结果 -->
      <van-tab title="测试结果" name="results">
        <div class="tab-content">
          <div class="section">
            <h3>📊 测试日志</h3>
            <div class="results-container">
              <div v-for="(log, index) in testLogs" :key="index" class="log-item" :class="log.type">
                <div class="log-content">
                  <span class="log-time">{{ log.time }}</span>
                  <span class="log-message">{{ log.message }}</span>
                </div>
                <div v-if="log.data" class="log-data">
                  <pre>{{ JSON.stringify(log.data, null, 2) }}</pre>
                </div>
              </div>
            </div>

            <div class="results-actions">
              <van-button @click="clearLogs" type="warning" size="small"> 清除日志 </van-button>
              <van-button @click="exportLogs" type="default" size="small"> 导出日志 </van-button>
            </div>
          </div>
        </div>
      </van-tab>
    </van-tabs>

    <!-- Site Key 选择器 -->
    <van-popup v-model:show="showSiteKeyPicker" position="bottom">
      <van-picker
        :columns="siteKeyOptions"
        @confirm="onSiteKeyConfirm"
        @cancel="showSiteKeyPicker = false"
      />
    </van-popup>

    <!-- 外观选择器 -->
    <van-popup v-model:show="showAppearancePicker" position="bottom">
      <van-picker
        :columns="appearanceOptions"
        @confirm="onAppearanceConfirm"
        @cancel="showAppearancePicker = false"
      />
    </van-popup>

    <!-- 组件验证弹窗 -->
    <CloudflareVerifyDialog
      v-model="showComponentDialog"
      :cf-type="CF_TURNSTILE_TYPE.LOGIN_SUBMIT"
      title="组件验证测试"
      description="这是直接使用组件的验证测试"
      @success="handleComponentSuccess"
      @error="handleComponentError"
      @cancel="handleComponentCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { showToast } from "vant";
import { CF_TURNSTILE_TYPE } from "@/utils/CloudflareMgr";
import {
  showCloudflareVerify,
  executeSeamlessVerification,
  verifyLogin,
  verifyRegister,
  verifyKYC,
  verifyWithdrawal,
  verifyForgetPassword,
  verifyChangePassword,
  type VerifyResult,
} from "@/utils/CloudflareVerifyAPI";
import CloudflareVerifyDialog from "@/components/CloudflareVerifyDialog/index.vue";

// 响应式状态
const activeTab = ref("dialog");
const currentEnv = ref(import.meta.env.MODE);

// 测试状态
const isTestingDialog = ref(false);
const isTestingLogin = ref(false);
const isTestingKYC = ref(false);
const showComponentDialog = ref(false);
const isTestingVerificationMgr = ref(false);
const isTestingSeamless = ref(false);

// 测试日志
const testLogs = ref<
  Array<{
    type: "success" | "error" | "warning" | "info";
    message: string;
    time: string;
    data?: any;
  }>
>([]);

// 添加日志
const addLog = (type: "success" | "error" | "warning" | "info", message: string, data?: any) => {
  const time = new Date().toLocaleTimeString();
  testLogs.value.unshift({ type, message, time, data });

  // 限制日志数量
  if (testLogs.value.length > 50) {
    testLogs.value = testLogs.value.slice(0, 50);
  }
};

// 标签页切换
const onTabChange = (name: string) => {
  addLog("info", `切换到 ${name} 标签页`);
};

// 弹窗验证测试
const testDialogVerification = async () => {
  if (isTestingDialog.value) return;

  isTestingDialog.value = true;
  addLog("info", "开始弹窗验证测试");

  try {
    const result = await showCloudflareVerify({
      cfType: CF_TURNSTILE_TYPE.LOGIN_SUBMIT,
      title: "测试验证",
      description: "这是一个测试验证弹窗",
    });

    isTestingDialog.value = false;

    if (result.success) {
      addLog("success", "弹窗验证成功", result);
      showToast("验证成功");
    } else {
      addLog("error", "弹窗验证失败", result);
      showToast(result.cancelled ? "验证已取消" : "验证失败");
    }
  } catch (error) {
    isTestingDialog.value = false;
    addLog("error", `弹窗验证测试失败: ${error}`);
    showToast("测试失败");
  }
};

// 登录验证测试
const testLoginVerification = async () => {
  if (isTestingLogin.value) return;

  isTestingLogin.value = true;
  addLog("info", "开始登录验证测试");

  try {
    const result = await verifyLogin();
    isTestingLogin.value = false;

    if (result.success) {
      addLog("success", "登录验证成功", result);
      showToast("登录验证成功");
    } else {
      addLog("error", "登录验证失败", result);
      showToast(result.cancelled ? "验证已取消" : "登录验证失败");
    }
  } catch (error) {
    isTestingLogin.value = false;
    addLog("error", `登录验证测试失败: ${error}`);
    showToast("测试失败");
  }
};

// KYC验证测试
const testKYCVerification = async () => {
  if (isTestingKYC.value) return;

  isTestingKYC.value = true;
  addLog("info", "开始KYC验证测试");

  try {
    const result = await verifyKYC();
    isTestingKYC.value = false;

    if (result.success) {
      addLog("success", "KYC验证成功", result);
      showToast("KYC验证成功");
    } else {
      addLog("error", "KYC验证失败", result);
      showToast(result.cancelled ? "验证已取消" : "KYC验证失败");
    }
  } catch (error) {
    isTestingKYC.value = false;
    addLog("error", `KYC验证测试失败: ${error}`);
    showToast("测试失败");
  }
};

// VerificationMgr 无感验证测试
const testVerificationMgrSeamless = async () => {
  if (isTestingVerificationMgr.value) return;

  isTestingVerificationMgr.value = true;
  addLog("info", "开始VerificationMgr无感验证测试");

  try {
    // 导入 VerificationMgr
    const { VerificationMgr } = await import("@/utils/VerificationMgr");

    // 使用 VerificationMgr 进行无感验证
    await VerificationMgr.instance.verify(
      "phone_code_login",
      (result) => {
        isTestingVerificationMgr.value = false;

        if (result && result.success) {
          addLog("success", "VerificationMgr无感验证成功", result);
          showToast("VerificationMgr无感验证成功");
        } else {
          addLog("error", "VerificationMgr无感验证失败", result);
          showToast("VerificationMgr无感验证失败");
        }
      },
      {
        seamless: true, // 使用无感验证
      }
    );
  } catch (error) {
    isTestingVerificationMgr.value = false;
    addLog("error", `VerificationMgr无感验证测试失败: ${error}`);
    showToast("测试失败");
  }
};

// executeSeamlessVerification 直接测试
const testSeamlessVerification = async () => {
  console.log("testSeamlessVerification", isTestingSeamless.value);
  if (isTestingSeamless.value) return;

  isTestingSeamless.value = true;
  addLog("info", "开始executeSeamlessVerification无感验证测试");

  try {
    const result = await executeSeamlessVerification({
      cfType: CF_TURNSTILE_TYPE.LOGIN_SUBMIT,
      theme: "light",
      size: "invisible",
      appearance: "interaction-only",
    });

    isTestingSeamless.value = false;

    if (result.success) {
      addLog("success", "executeSeamlessVerification验证成功", result);
      showToast("无感验证成功");
    } else {
      addLog("error", "executeSeamlessVerification验证失败", result);
      showToast(result.cancelled ? "验证已取消" : "无感验证失败");
    }
  } catch (error) {
    isTestingSeamless.value = false;
    addLog("error", `executeSeamlessVerification测试失败: ${error}`);
    showToast("测试失败");
  }
};

// 快捷验证方法
const quickTestLogin = async () => {
  addLog("info", "快捷登录验证");
  try {
    const result = await verifyLogin();
    handleQuickTestResult("登录验证", result);
  } catch (error) {
    addLog("error", `快捷登录验证失败: ${error}`);
  }
};

const quickTestRegister = async () => {
  addLog("info", "快捷注册验证");
  try {
    const result = await verifyRegister();
    handleQuickTestResult("注册验证", result);
  } catch (error) {
    addLog("error", `快捷注册验证失败: ${error}`);
  }
};

const quickTestKYC = async () => {
  addLog("info", "快捷KYC验证");
  try {
    const result = await verifyKYC();
    handleQuickTestResult("KYC验证", result);
  } catch (error) {
    addLog("error", `快捷KYC验证失败: ${error}`);
  }
};

const quickTestWithdrawal = async () => {
  addLog("info", "快捷提款验证");
  try {
    const result = await verifyWithdrawal();
    handleQuickTestResult("提款验证", result);
  } catch (error) {
    addLog("error", `快捷提款验证失败: ${error}`);
  }
};

const quickTestForgetPW = async () => {
  addLog("info", "快捷忘记密码验证");
  try {
    const result = await verifyForgetPassword();
    handleQuickTestResult("忘记密码验证", result);
  } catch (error) {
    addLog("error", `快捷忘记密码验证失败: ${error}`);
  }
};

const quickTestChangePW = async () => {
  addLog("info", "快捷修改密码验证");
  try {
    const result = await verifyChangePassword();
    handleQuickTestResult("修改密码验证", result);
  } catch (error) {
    addLog("error", `快捷修改密码验证失败: ${error}`);
  }
};

// 处理快捷测试结果
const handleQuickTestResult = (testName: string, result: VerifyResult) => {
  if (result.success) {
    addLog("success", `${testName}成功`, result);
    showToast(`${testName}成功`);
  } else {
    addLog("error", `${testName}失败`, result);
    showToast(result.cancelled ? "验证已取消" : `${testName}失败`);
  }
};

// 组件验证处理
const handleComponentSuccess = (result: any) => {
  addLog("success", "组件验证成功", result);
  showToast("组件验证成功");
  setTimeout(() => {
    showComponentDialog.value = false;
  }, 4000);
};

const handleComponentError = (error: string) => {
  addLog("error", "组件验证失败", { error });
  showToast("组件验证失败");
};

const handleComponentCancel = () => {
  addLog("info", "组件验证已取消");
  showToast("验证已取消");
  showComponentDialog.value = false;
};

// 清除日志
const clearLogs = () => {
  testLogs.value = [];
  showToast("日志已清除");
};

// 导出日志
const exportLogs = () => {
  const logData = testLogs.value.map((log) => ({
    time: log.time,
    type: log.type,
    message: log.message,
    data: log.data,
  }));

  const blob = new Blob([JSON.stringify(logData, null, 2)], { type: "application/json" });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = `cloudflare-test-logs-${new Date().toISOString().slice(0, 10)}.json`;
  a.click();
  URL.revokeObjectURL(url);

  addLog("success", "日志已导出");
  showToast("日志已导出");
};

// 初始化
onMounted(() => {
  addLog("info", "Cloudflare 测试页面已加载 (新版)");
  addLog("info", `当前环境: ${currentEnv.value}`);
});
</script>

<style scoped>
.cloudflare-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 20px;
}

.test-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.test-header p {
  color: #666;
  font-size: 14px;
}

.tab-content {
  padding: 20px 0;
}

.section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.test-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quick-test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-top: 16px;
}

.turnstile-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  margin-top: 20px;
  border: 2px dashed #e5e5e5;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.label {
  font-weight: 500;
  color: #666;
}

.value {
  font-size: 14px;
  color: #333;
}

.value.monospace {
  font-family: monospace;
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.diagnostic-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.external-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.results-container {
  max-height: 400px;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.log-item {
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  border-left: 4px solid;
}

.log-item.success {
  background: #f0f9ff;
  border-left-color: #07c160;
}

.log-item.error {
  background: #fef2f2;
  border-left-color: #ef4444;
}

.log-item.warning {
  background: #fffbeb;
  border-left-color: #f59e0b;
}

.log-item.info {
  background: #f8fafc;
  border-left-color: #64748b;
}

.log-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.log-time {
  font-size: 12px;
  color: #666;
  min-width: 80px;
}

.log-message {
  font-size: 14px;
  flex: 1;
}

.log-data {
  margin-top: 8px;
  padding: 8px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}

.log-data pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

.results-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .cloudflare-test {
    padding: 16px;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .diagnostic-buttons {
    grid-template-columns: repeat(2, 1fr);
  }

  .external-actions {
    flex-direction: column;
  }
}
</style>
