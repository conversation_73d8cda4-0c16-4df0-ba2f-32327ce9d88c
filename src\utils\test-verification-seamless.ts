/**
 * 测试验证管理器的无感验证功能
 */

import { VerificationMgr } from "./VerificationMgr";

/**
 * 测试 VerificationMgr 的无感验证功能
 */
export async function testVerificationMgrSeamless(): Promise<boolean> {
  console.log("🧪 Testing VerificationMgr seamless verification...");
  
  return new Promise((resolve) => {
    VerificationMgr.instance.verify(
      "phone_code_login",
      (result) => {
        if (result && result.success) {
          console.log("✅ VerificationMgr seamless verification successful:", result);
          console.log("Token:", result.data?.["cf-token"] || result.data?.cf_token);
          console.log("Scene:", result.data?.["cf-scene"] || result.data?.cf_type);
          resolve(true);
        } else {
          console.log("❌ VerificationMgr seamless verification failed:", result);
          resolve(false);
        }
      },
      {
        seamless: true, // 使用无感验证
      }
    );
  });
}

/**
 * 测试 VerificationMgr 的有感验证功能
 */
export async function testVerificationMgrDialog(): Promise<boolean> {
  console.log("🧪 Testing VerificationMgr dialog verification...");
  
  return new Promise((resolve) => {
    VerificationMgr.instance.verify(
      "phone_code_login",
      (result) => {
        if (result && result.success) {
          console.log("✅ VerificationMgr dialog verification successful:", result);
          console.log("Token:", result.data?.["cf-token"] || result.data?.cf_token);
          console.log("Scene:", result.data?.["cf-scene"] || result.data?.cf_type);
          resolve(true);
        } else {
          console.log("❌ VerificationMgr dialog verification failed:", result);
          resolve(false);
        }
      },
      {
        seamless: false, // 使用有感验证
      }
    );
  });
}

/**
 * 运行所有 VerificationMgr 测试
 */
export async function runAllVerificationMgrTests(): Promise<void> {
  console.log("🚀 Starting VerificationMgr tests...");
  
  const seamlessTest = await testVerificationMgrSeamless();
  
  console.log("\n📊 Test Results:");
  console.log(`VerificationMgr seamless verification: ${seamlessTest ? "✅ PASS" : "❌ FAIL"}`);
  
  if (seamlessTest) {
    console.log("🎉 VerificationMgr seamless verification test passed!");
  } else {
    console.log("❌ VerificationMgr seamless verification test failed!");
  }
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，可以通过控制台调用测试
  (window as any).testVerificationMgr = {
    seamless: testVerificationMgrSeamless,
    dialog: testVerificationMgrDialog,
    all: runAllVerificationMgrTests,
  };
  
  console.log("🧪 VerificationMgr tests available:");
  console.log("- testVerificationMgr.seamless()");
  console.log("- testVerificationMgr.dialog()");
  console.log("- testVerificationMgr.all()");
}
